import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:weather_app/models/weather_model.dart';
import 'package:weather_app/service/weather_service.dart';

class SearcchView extends StatelessWidget {
  const SearcchView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color.fromARGB(255, 243, 229, 33),
        title: const Text('Search a city'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: TextField(
            onSubmitted: (value) async {
              weatherModel = await WeatherService(Dio())
                  .GetCurrentWeather(cityName: value);
              Navigator.pop(context);
            },
            decoration: InputDecoration(
              hintText: 'Enter a city',
              labelText: 'Search ',
              labelStyle: TextStyle(
                color: const Color.fromARGB(255, 1, 8, 8),
              ),
              suffixIcon: Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(
                  color: const Color.fromARGB(255, 33, 240, 243),
                  width: 2,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(
                  color: const Color.fromARGB(255, 33, 240, 243),
                  width: 2,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

WeatherModel? weatherModel;
