import 'package:flutter/material.dart';
import 'package:weather_app/models/weather_model.dart';

class WeatherInfoBody extends StatelessWidget {
  const WeatherInfoBody({Key? key, required this.weather}) : super(key: key);
  final WeatherModel weather;

  List<Color> getGradientColors(String? weatherState) {
    if (weatherState == null) {
      return [Colors.blue.shade300, Colors.blue.shade100];
    }

    if (weatherState.contains("sunny") || weatherState.contains("clear")) {
      return [Colors.orange.shade400, Colors.yellow.shade200];
    } else if (weatherState.contains("partly cloudy")) {
      return [Colors.lightBlue.shade400, Colors.lightBlue.shade100];
    } else if (weatherState.contains("cloudy") ||
        weatherState.contains("overcast")) {
      return [Colors.grey.shade400, Colors.grey.shade200];
    } else if (weatherState.contains("rain")) {
      return [Colors.indigo.shade400, Colors.indigo.shade100];
    } else if (weatherState.contains("snow")) {
      return [Colors.blueGrey.shade300, Colors.white];
    } else {
      return [Colors.blue.shade300, Colors.blue.shade100];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: getGradientColors(weather.weatherStates),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              weather.cityName,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 32,
              ),
            ),
            Text(
              'updated at ${weather.date.hour}:${weather.date.minute}',
              style: const TextStyle(
                fontSize: 24,
              ),
            ),
            const SizedBox(
              height: 32,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Image.network(
                  weather.image.contains("https:")
                      ? weather.image
                      : "https:${weather.image}",
                ),
                Text(
                  weather.temp.toString(),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 32,
                  ),
                ),
                Column(
                  children: [
                    Text(
                      'Maxtemp: ${weather.maxTemp.round()}',
                      style: const TextStyle(
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      'Mintemp: ${weather.minTemp.round()}',
                      style: const TextStyle(
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(
              height: 32,
            ),
            Text(
              weather.weatherStates,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 32,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
