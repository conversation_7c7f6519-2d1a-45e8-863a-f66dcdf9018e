
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:weather_app/models/weather_model.dart';

class WeatherService {
  final String baseUrl = 'https://api.weatherapi.com/v1';
  final String keyApi = '10ab289672b74111abd142750252906%20';
  final Dio dio;
  WeatherService(this.dio);

  Future<WeatherModel> GetCurrentWeather({required String cityName}) async {
    try {
      Response response =
          await dio.get('$baseUrl/forecast.json?key=$keyApi&q=$cityName&days=1');
          log("hhhhhh${response.data}");
      WeatherModel weatherModel = WeatherModel.fromJson(response.data);
      return weatherModel;
    } on DioException catch (e) {
final String message = e.response?.data['error']['message']?? 'opps there is an error, try again later.';
throw Exception(message);
    }catch (e) {
      throw Exception('opps there is an error, try again later.');
    } 
  }
}
