import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:weather_app/CUBIT/Git_Weather_cubit/Git_Weather_State.dart';
import 'package:weather_app/models/weather_model.dart';
import 'package:weather_app/service/weather_service.dart';

class GitWeatherCubit extends Cubit<WeatherState> {
  GitWeatherCubit(WeatherState initialState) : super(NoWeatherState());


  GitWeather ({required String cityName}) async{
      try {
  WeatherModel weatherModel = await WeatherService(Dio()).GetCurrentWeather(cityName:cityName);
  
  emit(WeatherLodedState());
}  catch (e) {

emit(WeatherFailureState());
}
  }





}