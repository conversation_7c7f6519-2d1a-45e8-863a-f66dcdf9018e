{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\moaz\\the flutter App in liring\\weather_app_setup-main\\android\\app\\.cxx\\Debug\\1r414l41\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\moaz\\the flutter App in liring\\weather_app_setup-main\\android\\app\\.cxx\\Debug\\1r414l41\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}