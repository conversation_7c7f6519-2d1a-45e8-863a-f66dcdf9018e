import 'package:flutter/material.dart';
import 'package:weather_app/views/search_view.dart';
import 'package:weather_app/widgets/no_weather_body.dart';
// import 'package:weather_app/widgets/weather_info_body.dart';
import 'package:weather_app/CUBIT/Git_Weather_cubit/Git_Weather_Cubit.dart';
import 'package:weather_app/CUBIT/Git_Weather_cubit/Git_Weather_State.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:weather_app/widgets/weather_info_body.dart';

class HomeView extends StatelessWidget {
  const HomeView({Key? key}) : super(key: key);

  MaterialColor getThemeColor(String? value) {
    if (value == null) {
      return Colors.blue;
    }

    if (value.contains("sunny") || value.contains("clear")) {
      return Colors.orange;
    } else if (value.contains("partly cloudy")) {
      return Colors.lightBlue;
    } else if (value.contains("cloudy") || value.contains("overcast")) {
      return Colors.grey;
    } else if (value.contains("mist") || value.contains("fog")) {
      return Colors.blueGrey;
    } else if (value.contains("rain")) {
      return Colors.indigo;
    } else if (value.contains("sleet")) {
      return Colors.deepPurple;
    } else if (value.contains("snow") || value.contains("blizzard")) {
      return Colors.blueGrey;
    } else if (value.contains("thunder")) {
      return Colors.deepOrange;
    } else if (value.contains("drizzle")) {
      return Colors.teal;
    } else if (value.contains("ice pellets")) {
      return Colors.cyan;
    } else {
      return Colors.lightBlue;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GitWeatherCubit, WeatherState>(
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            backgroundColor: getThemeColor(
              state is WeatherLodedState
                  ? state.weatherModel.weatherStates
                  : null,
            ),
            title: const Text('Weather App'),
            actions: [
              IconButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const SearcchView(),
                    ),
                  );
                },
                icon: const Icon(Icons.search),
              ),
            ],
          ),
          body: BlocBuilder<GitWeatherCubit, WeatherState>(
            builder: (context, innerState) {
              if (innerState is WeatherInitialState) {
                return const NoWeatherBody();
              } else if (innerState is WeatherLodedState) {
                return WeatherInfoBody(
                  weather: innerState.weatherModel,
                );
              } else {
                return const Text('OPPS there is an error, try again later.');
              }
            },
          ),
        );
      },
    );
  }
}
